/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工保存请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工保存请求DTO
 * 用于员工新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSaveRequest {

    /**
     * 员工数据
     */
    private EmployeeData data;

    /**
     * 员工数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeData {

        /**
         * 员工ID（更新时必填）
         */
        private String id;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名，支持多语
         */
        private MultiLangName name;

        /**
         * 部门编码
         */
        private String dept_code;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 启用状态（1:启用，0:停用）
         */
        private Integer enable;

        /**
         * 操作状态（Insert:新增，Update:修改）
         */
        private String _status;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 性别（1:男，2:女）
         */
        private Integer gender;

        /**
         * 入职日期
         */
        private String entry_date;

        /**
         * 员工类型
         */
        private String emp_type;

        /**
         * 职位
         */
        private String position;

        /**
         * 工号
         */
        private String emp_no;

        private Integer sex;
    }

    /**
     * 多语言名称内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {

        /**
         * 中文名称
         */
        private String zh_CN;

        /**
         * 英文名称
         */
        private String en_US;
    }
}