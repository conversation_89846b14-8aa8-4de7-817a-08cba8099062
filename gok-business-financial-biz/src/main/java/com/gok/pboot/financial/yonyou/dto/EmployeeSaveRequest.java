/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工保存请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 员工保存请求DTO
 * 用于员工新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSaveRequest {

    /**
     * 员工数据
     */
    private EmployeeData data;

    /**
     * 员工数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeData {

        /**
         * 重新提交检查键
         */
        private String resubmitCheckKey;

        /**
         * 启用状态（1:启用，0:停用）
         */
        private Integer enable;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 照片
         */
        private String photo;

        /**
         * 员工ID（更新时必填）
         */
        private String id;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名
         */
        private String name;

        /**
         * 证件类型
         */
        private String cert_type;

        /**
         * 证件类型名称
         */
        private String cert_type_name;

        /**
         * 证件号码
         */
        private String cert_no;

        /**
         * 性别
         */
        private Integer sex;

        /**
         * 出生日期
         */
        private String birthdate;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 办公电话
         */
        private String officetel;

        /**
         * 业务员标识
         */
        private Boolean biz_man_tag;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 店铺助理标识
         */
        private Boolean shop_assis_tag;

        /**
         * 排序号
         */
        private Integer ordernumber;

        /**
         * 备注
         */
        private String remark;

        /**
         * 操作状态（Insert:新增，Update:修改）
         */
        private String _status;

        /**
         * 自定义字段
         */
        private Defines defines;

        /**
         * 主要工作列表
         */
        private List<MainJob> mainJobList;

        /**
         * 兼职工作列表
         */
        private List<PartTimeJob> ptJobList;

        /**
         * 银行账户列表
         */
        private List<BankAccount> bankAcctList;
    }

    /**
     * 自定义字段内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Defines {

        /**
         * ID
         */
        private String id;

        /**
         * 自定义字段1
         */
        private String define1;

        /**
         * 自定义字段2
         */
        private String define2;
    }

    /**
     * 主要工作内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MainJob {

        /**
         * ID
         */
        private String id;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 组织名称
         */
        private String org_id_name;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 部门名称
         */
        private String dept_id_name;

        /**
         * 人员类别ID
         */
        private String psncl_id;

        /**
         * 人员类别名称
         */
        private String psncl_id_name;

        /**
         * 职务ID
         */
        private String job_id;

        /**
         * 职务名称
         */
        private String job_id_name;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 岗位名称
         */
        private String post_id_name;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 职级名称
         */
        private String jobgrade_id_name;

        /**
         * 主管
         */
        private String director;

        /**
         * 主管名称
         */
        private String director_name;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 职责
         */
        private String responsibilities;

        /**
         * 状态
         */
        private String _status;
    }

    /**
     * 兼职工作内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PartTimeJob {

        /**
         * ID
         */
        private String id;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 组织名称
         */
        private String org_id_name;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 部门名称
         */
        private String dept_id_name;

        /**
         * 人员类别ID
         */
        private String psncl_id;

        /**
         * 人员类别名称
         */
        private String psncl_id_name;

        /**
         * 职务ID
         */
        private String job_id;

        /**
         * 职务名称
         */
        private String job_id_name;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 岗位名称
         */
        private String post_id_name;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 职级名称
         */
        private String jobgrade_id_name;

        /**
         * 主管
         */
        private String director;

        /**
         * 主管名称
         */
        private String director_name;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 职责
         */
        private String responsibilities;

        /**
         * 状态
         */
        private String _status;
    }

    /**
     * 银行账户内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BankAccount {

        /**
         * ID
         */
        private String id;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 账户
         */
        private String account;

        /**
         * 银行
         */
        private String bank;

        /**
         * 银行名称
         */
        private String bankname_name;

        /**
         * 银行名称
         */
        private String bankname;

        /**
         * 银行名称
         */
        private String bank_name;

        /**
         * 货币
         */
        private String currency;

        /**
         * 账户类型
         */
        private String accttype;

        /**
         * 账户名称
         */
        private String accountname;

        /**
         * 货币名称
         */
        private String currency_name;

        /**
         * 是否默认
         */
        private String isdefault;

        /**
         * 备注
         */
        private String memo;

        /**
         * 状态
         */
        private String _status;
    }
}