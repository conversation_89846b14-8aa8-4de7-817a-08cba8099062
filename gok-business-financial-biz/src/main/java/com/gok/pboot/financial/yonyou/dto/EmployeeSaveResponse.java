/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工保存响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工保存响应DTO
 * 用于接收员工保存操作的响应结果
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSaveResponse {

    /**
     * 响应状态码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应数据
     */
    private List<EmployeeResponseData> data;

    /**
     * 员工响应数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeResponseData {

        /**
         * 员工ID
         */
        private String id;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名
         */
        private String name;

        /**
         * 部门编码
         */
        private String dept_code;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 操作状态
         */
        private String _status;

        /**
         * 错误信息（如果有）
         */
        private String error_msg;

        /**
         * 是否成功
         */
        private Boolean success;
    }
}